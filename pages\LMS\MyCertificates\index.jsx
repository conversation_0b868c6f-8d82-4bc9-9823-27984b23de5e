import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import clsx from 'clsx'
import style from '../index.module.css'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import Button from '../../../components/UI/Button/Button'
import PdfThumbnail from '../../../svg/metronic/LMS_pdf.svg'
import { useRouter } from 'next/router'
import { COURSE_STATUSES } from '..'
import { useEnrolledCoursesData } from '../../../hooks/LMS/useEnrolledCoursesData'
import { useContext } from 'react'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'

export default function MyCertificates() {
  const router = useRouter()

  const { userID: userId } = useContext(UserProfileContext)
  const { rows, loading } = useEnrolledCoursesData(COURSE_STATUSES.COMPLETED, userId)

  console.log('certificatesData', rows)
  const tableheader = () => (
    <div className={style.prospecTable}>
      <span className="text-xl font-bold py-2">Completed Courses</span>
    </div>
  )

  const flowTemplate = (rowData) => (
    <div className="flex flex-column gap-3">
      <span>{rowData?.course?.title ?? ''}</span>
      <span style={{ fontSize: '14px', fontStyle: 'italic', fontWeight: '400' }}>by {rowData?.leadFullName}</span>
    </div>
  )
  const viewTemplate = (rowData) => (
    <Button
      theme="metronic"
      variant="outline"
      label="View Certificate"
      width="200px"
      disabled={!rowData?.isCompleted}
      onClick={() => router.push(`/LMS/MyCertificates/Certificates?mappingId=${rowData?.id}`)}
    />
  )
  const imageTemplate = () => <Image src={PdfThumbnail} alt="pdf" />

  const mockRows = [
    {
      number: '1',
      lesson: 'Build a free website with WordPress',
      status: 'status'
    },
    {
      number: '2',
      lesson: 'Build a free website with WordPress',
      status: 'status'
    }
  ]

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex align-items-center gap-2 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs title="My Certificates" breadcrumbItems={[{ label: 'My Certificates' }]} theme="metronic" />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: 'flex',
              width: '22vw'
            }}
          >
            <i className={clsx('pi pi-search', style.searchIcon)} style={{ top: '22px' }} />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        {loading ? (
          <p>Loading...</p>
        ) : (
          <DataTable header={tableheader} value={rows} className="custom-lead" showHeaders={false}>
            <Column field="number" body={imageTemplate} style={{ width: '10%' }} />
            <Column field="lesson" body={flowTemplate} style={{ width: '70%' }} />
            <Column field="status" body={viewTemplate} style={{ width: '20%' }} />
          </DataTable>
        )}
      </div>
    </PageContainer>
  )
}
